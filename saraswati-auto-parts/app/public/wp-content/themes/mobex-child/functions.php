<?php

function mobex_enovathemes_child_scripts() {
	wp_enqueue_style( 'mobex_enovathemes-parent-style', get_template_directory_uri() . '/style.css' );
	wp_enqueue_script( 'algolia-autocomplete-js', '//cdn.jsdelivr.net/npm/@algolia/autocomplete-js', array(), '', true );
}
add_action( 'wp_enqueue_scripts', 'mobex_enovathemes_child_scripts' );

function algolia_add_product_shared_attributes( array $shared_attributes, WP_Post $post ) {
	$product = wc_get_product( $post );

	$shared_attributes['price'] = wc_price( $product->get_price() );
	$shared_attributes['regular_price'] = wc_price( $product->get_regular_price() );
	$shared_attributes['sale_price'] = wc_price( $product->get_sale_price() );
	$shared_attributes['is_on_sale'] = $product->is_on_sale();
	$shared_attributes['sku'] = $product->get_sku();
	$shared_attributes['stock_quantity'] = $product->get_stock_quantity();
	$shared_attributes['title_clear'] = preg_replace( '/[.\-_]+/', '', $product->get_name() );
	$shared_attributes['title_no_space'] = preg_replace( '/[ ]+/', '', $product->get_name() );

	$clean_no_special = preg_replace( '/[^A-Za-z0-9]+/', '', $product->get_name() );

	$title_char_suffixes = array();
	$length = strlen( $clean_no_special );
	for ( $i = 0; $i < $length; $i++ ) {
		$title_char_suffixes[] = substr( $clean_no_special, $i );
	}
	$shared_attributes['title_char_suffixes'] = $title_char_suffixes;

	$chosen_category = null;

	$product_categories = wp_get_post_terms( $product->get_id(), 'product_cat', array( 'fields' => 'all' ) );

	foreach ( $product_categories as $category ) {
		// Check if the category has children
		$children = get_term_children( $category->term_id, 'product_cat' );

		// If the category has no children, or it is the most nested child
		if ( empty( $children ) || count( $children ) == 1 ) {
			$chosen_category = $category;
			break;
		}
	}

	if ( $chosen_category != null ) {
		$shared_attributes['direct_category'] = $chosen_category->name;
	}

	return $shared_attributes;
}

add_filter( 'algolia_post_product_shared_attributes', 'algolia_add_product_shared_attributes', 10, 2 );
add_filter( 'algolia_searchable_post_product_shared_attributes', 'algolia_add_product_shared_attributes', 10, 2 );

function algolia_algolia_exclude_post( $should_index, WP_Post $post ) {


	if ( false === $should_index ) {
		return false;
	}

	if ( get_post_type( $post ) != 'product' ) {
		return false;
	}

	if ( $product = wc_get_product( $post ) ) {
		$excluded = $product->get_catalog_visibility();
		// If not, don't index the post.
		if ( $excluded != 'visible' ) {
			return false;
		}
	}

	return true;
}
add_filter( 'algolia_should_index_searchable_post', 'algolia_algolia_exclude_post', 10, 2 );
add_filter( 'algolia_should_index_post', 'algolia_algolia_exclude_post', 10, 2 );

add_filter( 'algolia_config', 'custom_algolia_config', 10 );
function custom_algolia_config( $config ) {
	$config['indices']['searchable_posts'] = $config['indices']['posts_product'];
	return $config;
}

// Add checkbox option in customizer to enable/disable following changes
add_action( 'customize_register', 'mobex_child_customize_register' );
function mobex_child_customize_register( $wp_customize ) {
	$wp_customize->add_setting( 'mobex_child_enable_all_vehicle_fields_on_load', array(
		'default' => false,
		'transport' => 'refresh',
	) );

	$wp_customize->add_control( new WP_Customize_Control( $wp_customize, 'mobex_child_enable_all_vehicle_fields_on_load', array(
		'label' => __( 'Enable All Vehicle Fields on Load', 'mobex-child' ),
		'section' => 'vehicle_section',
		'type' => 'checkbox',
	) ) );
}

if ( get_theme_mod( 'mobex_child_enable_all_vehicle_fields_on_load' ) ) {

	add_action( 'wp_enqueue_scripts', 'mobex_child_update_vehicle_file_script', 0 );
	function mobex_child_update_vehicle_file_script() {
		wp_enqueue_script( 'widget-product-vehicle-filter', get_stylesheet_directory_uri() . '/assets/js/widget-product-vehicle-filter.js', array( 'jquery', 'plugins-combined' ), '', true );
	}

	add_action( 'admin_enqueue_scripts', 'mobex_child_admin_scripts_styles', 0 );
	function mobex_child_admin_scripts_styles() {
		wp_enqueue_script( 'mobex-admin', get_stylesheet_directory_uri() . '/assets/js/admin.js', array( 'jquery' ), '0.0.2', true );
	}

	if ( ! function_exists( 'enovathemes_addons_vehicle_first_param' ) ) {
		add_action( 'wp_ajax_fetch_vehicles_params', 'child_enovathemes_addons_fetch_vehicles_params', 0 );
		function child_enovathemes_addons_fetch_vehicles_params() {
			$vehicle_params = apply_filters( 'vehicle_params', '' );
			if ( $vehicle_params != false ) {

				$form_output = '';

				$i = 1;

				foreach ( $vehicle_params as $param ) {

					// Get all unique values for this specific parameter
					$param_values = enovathemes_addons_vehicle_first_param( $param, false );

					if ( $i == 1 ) {
						$form_output .= '<div class="select-wrapper"><select class="vehicle-param" name="' . $param . '" data-placeholder="' . esc_html__( "Choose" ) . ' ' . $param . '"><option></option>';
					} else {
						$form_output .= '<div class="select-wrapper"><select class="vehicle-param" name="' . $param . '" data-placeholder="' . esc_html__( "Choose" ) . ' ' . $param . '" multiple="multiple">';
					}

					// Populate all select fields with their respective options
					if ( ! is_wp_error( $param_values ) ) {
						foreach ( $param_values as $value ) {
							$form_output .= '<option value="' . $value . '">' . $value . '</option>';
						}
					}

					$form_output .= '</select></div>';

					$i++;
				}

				if ( ! empty( $form_output ) ) {
					$form_output = '<form class="vehicle-admin-filter">' . $form_output . '</form>';
				}

				$data = array();
				$data['form'] = $form_output;

				if ( isset( $_POST['products'] ) ) {
					$data['html'] = et_render_vehicles_table( $_POST['products'] );
				} elseif ( ! isset( $_POST['form'] ) ) {
					$data['html'] = et_render_vehicles_table( $_POST['post_id'] );
				}

				echo json_encode( $data );

			}
			die;
		}

		add_action( 'wp_ajax_fetch_product_vehicles', 'child_enovathemes_addons_fetch_product_vehicles', 0 );
		function child_enovathemes_addons_fetch_product_vehicles() {

			$vehicle_params = apply_filters( 'vehicle_params', '' );
			$post_attributes = json_decode( stripslashes( $_POST['attributes'] ), true );

			if ( $vehicle_params != false ) {

				$vehicles = array();
				$next = array();
				$meta_query = array();
				$vehicles_terms = false;
				$off = array( 'next', 'year', 'post_id' );

				foreach ( $post_attributes as $key => $value ) {

					if ( ! in_array( $key, $off ) && $value ) {

						$compare = ( is_array( $value ) ) ? "IN" : "=";

						$meta_query[] = [ 
							"key" => "vehicle_" . $key,
							"value" => $value,
							"compare" => $compare,
						];

					}

				}

				if ( ! empty( $meta_query ) ) {

					$meta_query["relation"] = "AND";

					$args = [ 
						"taxonomy" => "vehicles",
						"hide_empty" => false,
						"meta_query" => $meta_query,
					];

					$vehicles_terms = get_terms( $args );

				} elseif ( array_key_exists( 'year', $post_attributes ) && ! empty( $post_attributes['year'] ) ) {

					$args = [ 
						"taxonomy" => "vehicles",
						"hide_empty" => false,
					];

					$vehicles_terms = get_terms( $args );

					if ( ! is_wp_error( $vehicles_terms ) ) {
						$vehicles_terms_with_year = array();

						foreach ( $vehicles_terms as $vehicle ) {
							$year = get_term_meta( $vehicle->term_id, 'vehicle_year', true );
							$years = et_year_formatting( $year );

							if ( ( is_array( $post_attributes['year'] ) && array_intersect( $post_attributes['year'], $years ) ) || ( is_array( $years ) && in_array( $post_attributes['year'], $years ) ) ) {
								$vehicles_terms_with_year[] = $vehicle;
							}

						}

						if ( ! empty( $vehicles_terms_with_year ) ) {
							$vehicles_terms = $vehicles_terms_with_year;
						}

					}

				}

				if ( ! is_wp_error( $vehicles_terms ) ) {

					if ( isset( $post_attributes['year'] ) && ! empty( $post_attributes['year'] ) && ! empty( $meta_query ) ) {

						$vehicles_terms_with_year = array();

						foreach ( $vehicles_terms as $vehicle ) {
							$year = get_term_meta( $vehicle->term_id, 'vehicle_year', true );
							$years = et_year_formatting( $year );

							if ( ( is_array( $post_attributes['year'] ) && array_intersect( $post_attributes['year'], $years ) ) || ( is_array( $years ) && in_array( $post_attributes['year'], $years ) ) ) {
								$vehicles_terms_with_year[] = $vehicle;
							}

						}

						if ( ! empty( $vehicles_terms_with_year ) ) {
							$vehicles_terms = $vehicles_terms_with_year;
						}
					}

					foreach ( $vehicles_terms as $vehicle ) {

						$params = array();

						foreach ( $vehicle_params as $param ) {
							$param_value = esc_html( get_term_meta( $vehicle->term_id, 'vehicle_' . $param, true ) );

							if ( str_contains( $param_value, '*' ) ) {
								$param_value = str_replace( "*", date( 'Y' ), $param_value );
							}

							$params[ $param ] = $param_value;

						}

						// Collect values for all subsequent fields, not just the next one
						if ( isset( $post_attributes['next'] ) && ! empty( $post_attributes['next'] ) ) {

							// Find the index of the current field to determine subsequent fields
							$current_field_index = array_search( $post_attributes['next'], $vehicle_params );

							if ( $current_field_index !== false ) {
								// Get all subsequent fields
								$subsequent_fields = array_slice( $vehicle_params, $current_field_index );

								foreach ( $subsequent_fields as $field ) {
									if ( $field == "year" ) {
										$year = get_term_meta( $vehicle->term_id, 'vehicle_' . $field, true );
										$years = et_year_formatting( $year );

										if ( ! empty( $years ) ) {
											foreach ( $years as $year ) {
												$next[ $field ][] = $year;
											}
										}
									} else {
										$field_value = esc_html( get_term_meta( $vehicle->term_id, 'vehicle_' . $field, true ) );
										if ( ! empty( $field_value ) ) {
											$next[ $field ][] = $field_value;
										}
									}
								}
							}
						}

						if ( ! empty( $params ) ) {
							$vehicles[ $vehicle->term_id ] = $params;
						}

					}

					$vehicles = array_unique( $vehicles, SORT_REGULAR );
					$vehicles = array_filter( $vehicles );

					// Process the new multi-field next array structure
					foreach ( $next as $field => $values ) {
						$next[ $field ] = array_unique( $values, SORT_REGULAR );
						$next[ $field ] = array_filter( $next[ $field ] );
					}

					$output = '';

					if ( ! empty( $vehicles ) ) {

						$product_terms = ( isset( $post_attributes['post_id'] ) ) ? get_the_terms( $post_attributes['post_id'], 'vehicles' ) : false;
						$product_terms_array = array();

						if ( ! is_wp_error( $product_terms ) && $product_terms ) {
							foreach ( $product_terms as $term ) {
								$product_terms_array[] = $term->term_id;
							}
						}

						$tbody = $thead = '';

						foreach ( $vehicle_params as $param ) {
							$thead .= '<th>' . ucfirst( $param ) . '</th>';
						}

						if ( ! empty( $thead ) ) {
							$output .= '<thead><tr><th><input name="all" type="checkbox" value="*" /></th>' . $thead . '</tr></thead>';
						}

						foreach ( $vehicles as $key => $value ) {
							$tbody .= '<tr>';

							$checked = ( ! empty( $product_terms_array ) && in_array( $key, $product_terms_array ) ) ? 'checked' : '';

							$tbody .= '<td><input ' . $checked . ' name="' . $key . '" type="checkbox" value="' . $key . '" /></td>';
							foreach ( $value as $name => $val ) {
								if ( $name != 'name' ) {
									$tbody .= '<td>' . $val . '</td>';
								}
							}
							$tbody .= '</tr>';
						}

						if ( ! empty( $tbody ) ) {
							$output .= '<tbody>' . $tbody . '</tbody>';
						}

						$output .= '<tfoot class="hidden"><tr><td colspan="20"><a class="vehicle-assign-action button button-primary button-large" href="#">' . esc_html__( 'Update', 'enovathemes-addons' ) . '</a><input type="hidden" id="assign-nonce" name="assign-nonce" value="' . esc_attr( wp_create_nonce( 'vehicle-assign' ) ) . '"></td></tr></tfoot>';

					}

					// Generate output for all subsequent fields
					$next_outputs = array();

					if ( ! empty( $next ) ) {
						foreach ( $next as $field => $values ) {
							$field_output = '';
							foreach ( $values as $value ) {
								$field_output .= '<option value="' . $value . '">' . $value . '</option>';
							}
							$next_outputs[ $field ] = $field_output;
						}
					}

					$data = array();
					$data['html'] = $output;
					$data['next'] = $next_outputs; // Now contains all subsequent fields
					$data['dev'] = $post_attributes;

					$data_json = json_encode( $data );

					echo $data_json;

				}

			}

			die;
		}
	}
}